'use client'

import React from 'react'
import { Search } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { Breadcrumb, useBreadcrumb } from '@/components/breadcrumb'
import { LanguageSwitch } from '@/components/language-switcher'
import { ThemeSwitch } from '@/components/theme-switcher'
import { UserAvatar } from '@/components/user-avatar'
import { cn } from '@/lib/utils'
import type { BreadcrumbItem } from '@/types/breadcrumb'

interface TopNavBarProps {
  title?: string
  breadcrumbItems?: BreadcrumbItem[]
  showSearch?: boolean
  className?: string
}

export function TopNavBar({ 
  title, 
  breadcrumbItems, 
  showSearch = true,
  className 
}: TopNavBarProps) {
  const { generateBreadcrumb } = useBreadcrumb()

  const finalBreadcrumbItems = breadcrumbItems || generateBreadcrumb()

  return (
    <header className={cn(
      "sticky top-0 z-40 w-full bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
      className
    )}>
      <div className="flex h-16 items-center px-4 lg:px-4">
        {/* 左侧：侧边栏触发器 + 面包屑/标题 */}
        <div className="flex items-center flex-1">
          {/* 侧边栏触发器 */}
          <SidebarTrigger className="mr-3 cursor-pointer" />
          
          {/* 分割符 */}
          <div className="h-6 w-px bg-border mr-3"></div>

          {/* 面包屑导航或标题 */}
          <div className="flex-1 min-w-0">
            {title ? (
              <h1 className="text-xl font-semibold truncate">{title}</h1>
            ) : (
              <Breadcrumb items={finalBreadcrumbItems} />
            )}
          </div>
        </div>

        {/* 右侧：工具栏 */}
        <div className="flex items-center gap-2">
          {/* 移动端搜索按钮 */}
          {showSearch && (
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
            >
              <Search className="h-4 w-4" />
            </Button>
          )}

          {/* 主题和语言切换 */}
          <div className="flex items-center gap-2">
            <ThemeSwitch />
            <LanguageSwitch />
          </div>

          {/* 用户头像 */}
          <div className="ml-3">
            <UserAvatar />
          </div>
        </div>
      </div>

      {/* 移动端搜索栏（展开时显示） */}
      {showSearch && (
        <div className="border-t px-4 py-3 md:hidden">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索..."
              className="pl-10 w-full"
            />
          </div>
        </div>
      )}
    </header>
  )
}